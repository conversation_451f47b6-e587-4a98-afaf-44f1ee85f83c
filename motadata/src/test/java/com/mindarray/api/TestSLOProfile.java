/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.SLOProfileConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

/**
 * Test class for SLO Profile API operations.
 * Tests all CRUD operations and validates different SLO profile types:
 * - Monitor Availability SLO
 * - Monitor Performance SLO  
 * - Instance Availability SLO
 * - Instance Performance SLO
 *
 * <AUTHOR> Thakkar
 */
@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(60 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestSLOProfile
{
    private static final String SLO_PROFILE_API_ENDPOINT = "/api/v1/settings/slo-profiles";
    
    private static final JsonObject context = new JsonObject();
    
    private static final Logger LOGGER = new Logger(TestSLOProfile.class, MOTADATA_API, "Test SLO Profile");
    
    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));
        
        testContext.completeNow();
    }
    
    /**
     * Test creating Monitor Availability SLO Profile (Payload 1)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testCreateMonitorAvailabilitySLOProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var timestamp = System.currentTimeMillis() / 1000;
        
        var payload = new JsonObject()
            .put("slo.profile.name", "MonitorAvailabilitySLO")
            .put("slo.profile.type", "Availability")
            .put("slo.profile.business.service.name", "Airtel")
            .put("slo.profile.tags", new JsonArray().add("monitor").add("availability"))
            .put("slo.profile.context", new JsonObject()
                .put("entity.type", "group")
                .put("metric", "status")
                .put("slo.instance", "monitor")
                .put("entities", new JsonArray().add(10000000000017L))
                .put("slo.target", 90)
                .put("slo.warning", 95)
                .put("filters", new JsonObject().put("data.filter", new JsonObject()))
                .put("slo.frequency", "Daily"))
            .put("slo.profile.start.time", String.valueOf(timestamp));
            
        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(SLOProfileConfigStore.getStore(), response.bodyAsJsonObject(), 
                String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.SLO_PROFILE.getName()), 
                LOGGER, testInfo.getTestMethod().get().getName());
                
            context.put("monitorAvailabilityId", response.bodyAsJsonObject().getLong(ID));
            
            testContext.completeNow();
        })));
    }
    
    /**
     * Test creating Monitor Performance SLO Profile (Payload 2)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateMonitorPerformanceSLOProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var timestamp = System.currentTimeMillis() / 1000;
        
        var payload = new JsonObject()
            .put("slo.profile.name", "MonitorPerformanceSLO")
            .put("slo.profile.type", "Performance")
            .put("slo.profile.business.service.name", "Airtel-Performance")
            .put("slo.profile.context", new JsonObject()
                .put("entity.type", "group")
                .put("metric", "system.cpu.percent")
                .put("slo.instance", "monitor")
                .put("slo.severity", new JsonObject()
                    .put("CRITICAL", new JsonObject()
                        .put("policy.condition", ">=")
                        .put("policy.threshold", "15")))
                .put("entities", new JsonArray().add(10000000000017L))
                .put("slo.target", 90)
                .put("slo.warning", 95)
                .put("filters", new JsonObject().put("data.filter", new JsonObject()))
                .put("slo.frequency", "Daily"))
            .put("slo.profile.start.time", String.valueOf(timestamp));
            
        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(SLOProfileConfigStore.getStore(), response.bodyAsJsonObject(), 
                String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.SLO_PROFILE.getName()), 
                LOGGER, testInfo.getTestMethod().get().getName());
                
            context.put("monitorPerformanceId", response.bodyAsJsonObject().getLong(ID));
            
            testContext.completeNow();
        })));
    }
    
    /**
     * Test creating Instance Availability SLO Profile (Payload 3)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateInstanceAvailabilitySLOProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var timestamp = System.currentTimeMillis() / 1000;
        
        var payload = new JsonObject()
            .put("slo.profile.name", "InstanceAvailabilitySLO")
            .put("slo.profile.type", "Availability")
            .put("slo.profile.business.service.name", "Airtel")
            .put("slo.profile.context", new JsonObject()
                .put("entity.type", "monitor")
                .put("metric", "status")
                .put("slo.instance", "process")
                .put("plugins", new JsonArray()
                    .add("linuxprocess")
                    .add("windowsprocess")
                    .add("solarisprocess")
                    .add("ibmaixprocess")
                    .add("hpuxprocess"))
                .put("entities", new JsonArray().add(77789039783L))
                .put("slo.target", 90)
                .put("slo.warning", 95)
                .put("filters", new JsonObject().put("data.filter", new JsonObject()))
                .put("slo.frequency", "Daily"))
            .put("slo.profile.start.time", String.valueOf(timestamp));
            
        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(SLOProfileConfigStore.getStore(), response.bodyAsJsonObject(), 
                String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.SLO_PROFILE.getName()), 
                LOGGER, testInfo.getTestMethod().get().getName());
                
            context.put("instanceAvailabilityId", response.bodyAsJsonObject().getLong(ID));
            
            testContext.completeNow();
        })));
    }
    
    /**
     * Test creating Instance Performance SLO Profile (Payload 4)
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testCreateInstancePerformanceSLOProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var timestamp = System.currentTimeMillis() / 1000;

        var payload = new JsonObject()
            .put("slo.profile.name", "InstancePerformanceSLO")
            .put("slo.profile.type", "Performance")
            .put("slo.profile.business.service.name", "Airtel")
            .put("slo.profile.context", new JsonObject()
                .put("entity.type", "monitor")
                .put("metric", "system.process~cpu.percent")
                .put("slo.instance", "process")
                .put("slo.severity", new JsonObject()
                    .put("CRITICAL", new JsonObject()
                        .put("policy.condition", ">=")
                        .put("policy.threshold", "0")))
                .put("plugins", new JsonArray()
                    .add("linuxprocess")
                    .add("windowsprocess")
                    .add("solarisprocess")
                    .add("ibmaixprocess")
                    .add("hpuxprocess"))
                .put("entities", new JsonArray().add(77789039783L))
                .put("slo.target", 90)
                .put("slo.warning", 95)
                .put("filters", new JsonObject().put("data.filter", new JsonObject()))
                .put("slo.frequency", "Daily"))
            .put("slo.profile.start.time", String.valueOf(timestamp));

        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, payload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(SLOProfileConfigStore.getStore(), response.bodyAsJsonObject(),
                String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.SLO_PROFILE.getName()),
                LOGGER, testInfo.getTestMethod().get().getName());

            context.put("instancePerformanceId", response.bodyAsJsonObject().getLong(ID));

            testContext.completeNow();
        })));
    }

    /**
     * Test getting all SLO profiles
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGetAllSLOProfiles(VertxTestContext testContext)
    {
        TestAPIUtil.get(SLO_PROFILE_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertGETAllRequestTestResult(response, SLOProfileConfigStore.getStore(), null);

            testContext.completeNow();
        })));
    }

    /**
     * Test getting a single SLO profile by ID
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testGetSLOProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var profileId = context.getLong("monitorAvailabilityId");

        TestAPIUtil.get(SLO_PROFILE_API_ENDPOINT + "/" + profileId, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertGETRequestTestResult(response, profileId, SLOProfileConfigStore.getStore(), null,
                LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));
    }

    /**
     * Test updating an SLO profile
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testUpdateSLOProfile(VertxTestContext testContext, TestInfo testInfo)
    {
        var profileId = context.getLong("monitorAvailabilityId");

        var updatePayload = new JsonObject()
            .put("slo.profile.business.service.name", "Airtel-Updated")
            .put("slo.profile.tags", new JsonArray().add("updated").add("monitor"))
            .put("slo.profile.context", new JsonObject()
                .put("entity.type", "group")
                .put("metric", "status")
                .put("slo.instance", "monitor")
                .put("entities", new JsonArray().add(10000000000017L))
                .put("slo.target", 95)
                .put("slo.warning", 98)
                .put("filters", new JsonObject().put("data.filter", new JsonObject()))
                .put("slo.frequency", "Weekly"));

        TestAPIUtil.put(SLO_PROFILE_API_ENDPOINT + "/" + profileId, updatePayload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertUpdateEntityTestResult(SLOProfileConfigStore.getStore(), updatePayload, response.bodyAsJsonObject(),
                String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.SLO_PROFILE.getName()),
                LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();
        })));
    }

    /**
     * Test getting active SLO profiles with filter
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testGetActiveSLOProfiles(VertxTestContext testContext)
    {
        var filterParam = "{\"active\":\"yes\"}";

        TestAPIUtil.get(SLO_PROFILE_API_ENDPOINT + "?filter=" + filterParam, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            Assertions.assertEquals(200, result.getInteger(RESPONSE_CODE));
            Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));
            Assertions.assertTrue(result.containsKey(RESULT));

            var profiles = result.getJsonArray(RESULT);
            Assertions.assertNotNull(profiles);

            testContext.completeNow();
        })));
    }

    /**
     * Test creating SLO profile with invalid type - should fail validation
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testCreateSLOProfileWithInvalidType(VertxTestContext testContext)
    {
        var timestamp = System.currentTimeMillis() / 1000;

        var invalidPayload = new JsonObject()
            .put("slo.profile.name", "InvalidTypeSLO")
            .put("slo.profile.type", "InvalidType") // Invalid type - should be Availability or Performance
            .put("slo.profile.business.service.name", "TestService")
            .put("slo.profile.context", new JsonObject()
                .put("entity.type", "group")
                .put("metric", "status")
                .put("slo.instance", "monitor")
                .put("entities", new JsonArray().add(10000000000017L))
                .put("slo.target", 90)
                .put("slo.warning", 95)
                .put("filters", new JsonObject().put("data.filter", new JsonObject()))
                .put("slo.frequency", "Daily"))
            .put("slo.profile.start.time", String.valueOf(timestamp));

        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, invalidPayload, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            Assertions.assertEquals(SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));
            Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));
            Assertions.assertTrue(result.containsKey(MESSAGE));

            testContext.completeNow();
        })));
    }

    /**
     * Test creating SLO profile with missing required fields - should fail validation
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testCreateSLOProfileWithMissingRequiredFields(VertxTestContext testContext)
    {
        var incompletePayload = new JsonObject()
            .put("slo.profile.name", "IncompleteSLO")
            // Missing required fields: slo.profile.type, slo.profile.business.service.name, slo.profile.context, slo.profile.start.time
            .put("slo.profile.tags", new JsonArray().add("test"));

        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, incompletePayload, testContext.succeeding(response -> testContext.verify(() ->
        {
            var result = response.bodyAsJsonObject();

            Assertions.assertEquals(SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));
            Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));
            Assertions.assertTrue(result.containsKey(MESSAGE));

            testContext.completeNow();
        })));
    }

    /**
     * Test creating SLO profile with duplicate name - should fail uniqueness validation
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testCreateSLOProfileWithDuplicateName(VertxTestContext testContext)
    {
        var timestamp = System.currentTimeMillis() / 1000;

        var duplicatePayload = new JsonObject()
            .put("slo.profile.name", "MonitorAvailabilitySLO") // Duplicate name from first test
            .put("slo.profile.type", "Availability")
            .put("slo.profile.business.service.name", "TestService")
            .put("slo.profile.context", new JsonObject()
                .put("entity.type", "group")
                .put("metric", "status")
                .put("slo.instance", "monitor")
                .put("entities", new JsonArray().add(10000000000017L))
                .put("slo.target", 90)
                .put("slo.warning", 95)
                .put("filters", new JsonObject().put("data.filter", new JsonObject()))
                .put("slo.frequency", "Daily"))
            .put("slo.profile.start.time", String.valueOf(timestamp));

        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, duplicatePayload, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(),
                String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, APIConstants.Entity.SLO_PROFILE.getName()),
                SLOProfileConfigStore.getStore(), "slo.profile.name", "MonitorAvailabilitySLO");

            testContext.completeNow();
        })));
    }

    /**
     * Test deleting SLO profiles
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testDeleteSLOProfiles(VertxTestContext testContext)
    {
        var profileId = context.getLong("instancePerformanceId");

        TestAPIUtil.delete(SLO_PROFILE_API_ENDPOINT + "/" + profileId, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(SLOProfileConfigStore.getStore(), response.bodyAsJsonObject(),
                String.format(InfoMessageConstants.ENTITY_DELETED, APIConstants.Entity.SLO_PROFILE.getName()));

            testContext.completeNow();
        })));
    }

    /**
     * Test tag handling in SLO profile creation and retrieval
     */
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testSLOProfileTagHandling(VertxTestContext testContext, TestInfo testInfo)
    {
        var timestamp = System.currentTimeMillis() / 1000;

        var payloadWithTags = new JsonObject()
            .put("slo.profile.name", "TaggedSLOProfile")
            .put("slo.profile.type", "Availability")
            .put("slo.profile.business.service.name", "TaggedService")
            .put("slo.profile.tags", new JsonArray().add("tag1").add("tag2").add("tag3"))
            .put("slo.profile.context", new JsonObject()
                .put("entity.type", "group")
                .put("metric", "status")
                .put("slo.instance", "monitor")
                .put("entities", new JsonArray().add(10000000000017L))
                .put("slo.target", 90)
                .put("slo.warning", 95)
                .put("filters", new JsonObject().put("data.filter", new JsonObject()))
                .put("slo.frequency", "Daily"))
            .put("slo.profile.start.time", String.valueOf(timestamp));

        TestAPIUtil.post(SLO_PROFILE_API_ENDPOINT, payloadWithTags, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertCreateEntityTestResult(SLOProfileConfigStore.getStore(), response.bodyAsJsonObject(),
                String.format(InfoMessageConstants.ENTITY_CREATED, APIConstants.Entity.SLO_PROFILE.getName()),
                LOGGER, testInfo.getTestMethod().get().getName());

            var createdId = response.bodyAsJsonObject().getLong(ID);

            // Verify tags are properly handled by retrieving the created profile
            TestAPIUtil.get(SLO_PROFILE_API_ENDPOINT + "/" + createdId, testContext.succeeding(getResponse -> testContext.verify(() ->
            {
                var retrievedProfile = getResponse.bodyAsJsonObject().getJsonObject(RESULT);

                Assertions.assertTrue(retrievedProfile.containsKey("slo.profile.tags"));

                var tags = retrievedProfile.getJsonArray("slo.profile.tags");
                Assertions.assertNotNull(tags);
                Assertions.assertTrue(tags.size() >= 3); // Should contain at least the 3 tags we added

                testContext.completeNow();
            })));
        })));
    }
}
